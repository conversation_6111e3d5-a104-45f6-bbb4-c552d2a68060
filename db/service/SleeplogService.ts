import Realm from "realm";
import { getRealm } from "../realm";

// Helper function to generate title based on sleep data
const generateSleepTitle = (sleepTime: Date, isCompleted: boolean) => {
  try {
    // Ensure sleepTime is a valid Date object
    const date = sleepTime instanceof Date ? sleepTime : new Date(sleepTime);

    if (isNaN(date.getTime())) {
      // If date is invalid, return default title
      return isCompleted ? "Sleep Session" : "Sleep Time";
    }

    const hour = date.getHours();

    if (isCompleted) {
      // For completed sleep sessions
      if (hour >= 22 || hour <= 6) return "Night Sleep";
      if (hour >= 12 && hour <= 16) return "Afternoon Nap";
      return "Sleep Session";
    } else {
      // For going to sleep logs
      if (hour >= 22 || hour <= 6) return "Going to Sleep";
      if (hour >= 12 && hour <= 16) return "Nap Time";
      return "Sleep Time";
    }
  } catch (error) {
    console.error("Error generating sleep title:", error);
    return isCompleted ? "Sleep Session" : "Sleep Time";
  }
};

// Helper function to calculate sleep quality based on duration
const calculateQualityFromDuration = (duration: number, sleepTime: Date) => {
  const hour = sleepTime.getHours();
  const isNightSleep = hour >= 22 || hour <= 6;

  if (isNightSleep) {
    // Night sleep: 7-9 hours is optimal
    if (duration >= 420 && duration <= 540) return 5; // 7-9 hours = Excellent
    if (duration >= 360 && duration <= 600) return 4; // 6-10 hours = Very Good
    if (duration >= 300 && duration <= 660) return 3; // 5-11 hours = Good
    if (duration >= 240) return 2; // 4+ hours = Fair
    return 1; // Less than 4 hours = Poor
  } else {
    // Naps: 20-90 minutes is optimal
    if (duration >= 20 && duration <= 90) return 5; // Optimal nap
    if (duration >= 10 && duration <= 120) return 4; // Good nap
    if (duration >= 5 && duration <= 180) return 3; // Decent nap
    if (duration >= 1) return 2; // Very short nap
    return 1; // Too short
  }
};

// Helper function to calculate quality based on sleep timing
const calculateQualityFromTiming = (sleepTime: Date, wakeTime: Date) => {
  const sleepHour = sleepTime.getHours();
  const wakeHour = wakeTime.getHours();

  // Optimal sleep window: 10 PM - 6 AM
  let score = 3; // Base score

  // Bonus for good sleep timing
  if (sleepHour >= 22 || sleepHour <= 2) score += 1;
  if (wakeHour >= 6 && wakeHour <= 8) score += 1;

  // Penalty for poor timing
  if (sleepHour >= 3 && sleepHour <= 21) score -= 1;
  if (wakeHour >= 11 || wakeHour <= 5) score -= 1;

  return Math.max(1, Math.min(5, score));
};

// Main function to calculate automatic sleep quality
export const calculateAutoSleepQuality = (sleepTime: Date, wakeTime: Date, duration: number) => {
  try {
    if (!duration || duration <= 0) return 3; // Default if no duration

    // Ensure dates are valid Date objects
    const sleep = sleepTime instanceof Date ? sleepTime : new Date(sleepTime);
    const wake = wakeTime instanceof Date ? wakeTime : new Date(wakeTime);

    if (isNaN(sleep.getTime()) || isNaN(wake.getTime())) {
      return 3; // Default if invalid dates
    }

    const durationScore = calculateQualityFromDuration(duration, sleep);
    const timingScore = calculateQualityFromTiming(sleep, wake);

    // Weighted average: 60% duration, 40% timing
    const autoQuality = Math.round((durationScore * 0.6) + (timingScore * 0.4));

    return Math.max(1, Math.min(5, autoQuality));
  } catch (error) {
    console.error("Error calculating auto sleep quality:", error);
    return 3; // Default quality on error
  }
};

export const createSleeplog = async ({
  sleepTime,
  wakeTime,
  isCompleted,
  notes,
  duration,
  quality,
  createdAt,
}: {
  sleepTime: Date;
  wakeTime?: Date;
  isCompleted: boolean;
  notes?: string;
  duration?: number;
  quality?: number;
  createdAt: Date;
}) => {
  try {
    const realm = await getRealm();
    let newSleeplogId = null;

    realm.write(() => {
      console.log("Creating sleeplog with data:", {
        sleepTime,
        wakeTime,
        isCompleted,
        notes,
        duration,
        quality,
        createdAt,
      });

      const sleeplog = realm.create("Sleeplog", {
        _id: new Realm.BSON.ObjectId(),
        sleepTime,
        wakeTime,
        isCompleted,
        notes,
        duration,
        quality,
        createdAt,
      });
      newSleeplogId = sleeplog._id;
      console.log("Sleeplog created successfully with ID:", newSleeplogId);
    });

    return newSleeplogId;
  } catch (error) {
    console.error("Error creating sleeplog:", error);
    throw error;
  }
};

export const getAllSleeplogs = async () => {
  const realm = await getRealm();
  const sleeplogs = realm.objects("Sleeplog").sorted("createdAt", true);

  return sleeplogs.map((sleeplog: any) => {
    // Ensure dates are properly converted
    const sleepTime = new Date(sleeplog.sleepTime);
    const wakeTime = sleeplog.wakeTime ? new Date(sleeplog.wakeTime) : undefined;
    const createdAt = new Date(sleeplog.createdAt);

    // Auto-calculate quality if not set and sleep is completed
    let quality = sleeplog.quality;
    if (!quality && sleeplog.isCompleted && sleeplog.duration && wakeTime) {
      quality = calculateAutoSleepQuality(sleepTime, wakeTime, sleeplog.duration);
    }

    return {
      id: sleeplog._id,
      title: generateSleepTitle(sleepTime, sleeplog.isCompleted),
      sleepTime: sleepTime,
      wakeTime: wakeTime,
      isCompleted: sleeplog.isCompleted,
      notes: sleeplog.notes,
      duration: sleeplog.duration,
      quality: quality,
      autoQuality: sleeplog.isCompleted && sleeplog.duration && wakeTime
        ? calculateAutoSleepQuality(sleepTime, wakeTime, sleeplog.duration)
        : undefined,
      createdAt: createdAt,
    };
  });
};

export const updateSleeplog = async (
  id: Realm.BSON.ObjectId,
  updates: Partial<{
    sleepTime: Date;
    wakeTime?: Date;
    isCompleted: boolean;
    notes?: string;
    duration?: number;
    quality?: number;
  }>
) => {
  const realm = await getRealm();
  try {
    const sleeplog = realm.objectForPrimaryKey("Sleeplog", id);
    if (sleeplog) {
      realm.write(() => {
        Object.assign(sleeplog, updates);
      });
    }
  } finally {
    // Optional cleanup
  }
};

export const deleteSleeplog = async (id: Realm.BSON.ObjectId) => {
  const realm = await getRealm();
  try {
    const sleeplog = realm.objectForPrimaryKey("Sleeplog", id);
    if (sleeplog) {
      realm.write(() => {
        realm.delete(sleeplog);
      });
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error deleting sleeplog:", error);
    return false;
  }
};

export const getSleeplogById = async (id: string) => {
  const realm = await getRealm();
  const sleeplog = realm.objectForPrimaryKey("Sleeplog", new Realm.BSON.ObjectId(id));
  if (sleeplog) {
    const data = JSON.parse(JSON.stringify(sleeplog));

    // Ensure dates are properly converted
    data.sleepTime = new Date(data.sleepTime);
    data.wakeTime = data.wakeTime ? new Date(data.wakeTime) : undefined;
    data.createdAt = new Date(data.createdAt);

    data.title = generateSleepTitle(data.sleepTime, data.isCompleted);
    return data;
  }
  return null;
};

export const getSleeplogCount = async () => {
  const realm = await getRealm();
  const allSleeplogs = realm.objects("Sleeplog");
  const total = allSleeplogs.length;
  const completed = allSleeplogs.filtered("isCompleted == true").length;
  const incomplete = total - completed;

  return {
    total,
    completed,
    incomplete,
  };
};

export const getSleeplogStats = async (
  groupBy: "weekly" | "monthly" | "allTime" = "weekly"
) => {
  const realm = await getRealm();
  const allSleeplogs = realm.objects("Sleeplog");

  if (groupBy === "allTime") {
    let total = 0;
    let totalDuration = 0;
    let averageQuality = 0;
    let qualityCount = 0;

    allSleeplogs.forEach((item: any) => {
      total += 1;
      if (item.duration) {
        totalDuration += item.duration;
      }
      if (item.quality) {
        averageQuality += item.quality;
        qualityCount += 1;
      }
    });

    return {
      total,
      totalDuration,
      averageQuality: qualityCount > 0 ? averageQuality / qualityCount : 0,
    };
  }

  // For weekly and monthly stats
  const now = new Date();
  let startDate: Date;

  if (groupBy === "weekly") {
    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  } else {
    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
  }

  const filteredSleeplogs = allSleeplogs.filtered(
    "createdAt >= $0",
    startDate
  );

  let total = 0;
  let totalDuration = 0;
  let averageQuality = 0;
  let qualityCount = 0;

  filteredSleeplogs.forEach((item: any) => {
    total += 1;
    if (item.duration) {
      totalDuration += item.duration;
    }
    if (item.quality) {
      averageQuality += item.quality;
      qualityCount += 1;
    }
  });

  return {
    total,
    totalDuration,
    averageQuality: qualityCount > 0 ? averageQuality / qualityCount : 0,
    period: groupBy,
  };
};

export const getRecentSleeplogs = async (limit: number = 10) => {
  const realm = await getRealm();
  const recentSleeplogs = realm
    .objects("Sleeplog")
    .sorted("createdAt", true)
    .slice(0, limit);

  return recentSleeplogs.map((sleeplog: any) => {
    // Ensure dates are properly converted
    const sleepTime = new Date(sleeplog.sleepTime);
    const wakeTime = sleeplog.wakeTime ? new Date(sleeplog.wakeTime) : undefined;
    const createdAt = new Date(sleeplog.createdAt);

    return {
      id: sleeplog._id,
      title: generateSleepTitle(sleepTime, sleeplog.isCompleted),
      sleepTime: sleepTime,
      wakeTime: wakeTime,
      isCompleted: sleeplog.isCompleted,
      notes: sleeplog.notes,
      duration: sleeplog.duration,
      quality: sleeplog.quality,
      createdAt: createdAt,
    };
  });
};

export const getSleeplogsByDateRange = async (
  startDate: Date,
  endDate: Date
) => {
  const realm = await getRealm();
  const sleeplogs = realm
    .objects("Sleeplog")
    .filtered("createdAt >= $0 AND createdAt <= $1", startDate, endDate)
    .sorted("createdAt", true);

  return sleeplogs.map((sleeplog: any) => {
    // Ensure dates are properly converted
    const sleepTime = new Date(sleeplog.sleepTime);
    const wakeTime = sleeplog.wakeTime ? new Date(sleeplog.wakeTime) : undefined;
    const createdAt = new Date(sleeplog.createdAt);

    return {
      id: sleeplog._id,
      title: generateSleepTitle(sleepTime, sleeplog.isCompleted),
      sleepTime: sleepTime,
      wakeTime: wakeTime,
      isCompleted: sleeplog.isCompleted,
      notes: sleeplog.notes,
      duration: sleeplog.duration,
      quality: sleeplog.quality,
      createdAt: createdAt,
    };
  });
};

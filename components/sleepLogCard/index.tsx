import React, { useState } from "react";
import { View, Text, StyleSheet, TouchableOpacity, Alert, Modal, Platform, Pressable } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import SleepQualityDisplay from "../sleepQualityDisplay";
import { CrimsonLuxe } from "@/constants/Colors";
import DateTimePicker from "@react-native-community/datetimepicker";
import { updateSleeplog, calculateAutoSleepQuality } from "@/db/service/SleeplogService";
import LottieView from "lottie-react-native";
import Realm from "realm";

interface SleepLogCardProps {
  sleeplog: {
    id: string;
    title: string;
    sleepTime: Date;
    wakeTime?: Date;
    isCompleted: boolean;
    notes?: string;
    duration?: number; // in minutes
    quality?: number; // 1-5 scale
    autoQuality?: number; // auto-calculated quality
    createdAt: Date;
  };
  onPress?: () => void;
  onUpdate?: () => void; // Callback when sleep log is updated
  showQuality?: boolean;
}

const SleepLogCard: React.FC<SleepLogCardProps> = ({
  sleeplog,
  onPress,
  onUpdate,
  showQuality = true,
}) => {
  const [showWakeTimePicker, setShowWakeTimePicker] = useState(false);
  const [selectedWakeTime, setSelectedWakeTime] = useState(new Date());
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [completionData, setCompletionData] = useState<{ duration: number; quality: number } | null>(null);
  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return "N/A";
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString([], {
      month: "short",
      day: "numeric",
    });
  };

  const handleWakeUp = () => {
    if (sleeplog.isCompleted) {
      Alert.alert("Already Completed", "This sleep session is already completed.");
      return;
    }

    Alert.alert(
      "Wake Up Time",
      "Set your wake up time to complete this sleep session",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Set Wake Time",
          onPress: () => {
            setSelectedWakeTime(new Date());
            setShowWakeTimePicker(true);
          },
        },
      ]
    );
  };

  const handleWakeTimeConfirm = async (selectedTime: Date) => {
    setShowWakeTimePicker(false);

    try {
      const sleepTime = new Date(sleeplog.sleepTime);
      const wakeTime = selectedTime;

      // Calculate duration
      const diffMs = wakeTime.getTime() - sleepTime.getTime();
      const duration = Math.floor(diffMs / (1000 * 60));

      if (duration <= 0) {
        Alert.alert("Invalid Time", "Wake time must be after sleep time.");
        return;
      }

      // Auto-calculate quality
      const quality = calculateAutoSleepQuality(sleepTime, wakeTime, duration);

      // Update the sleep log
      await updateSleeplog(new Realm.BSON.ObjectId(sleeplog.id), {
        wakeTime,
        duration,
        quality,
        isCompleted: true,
      });

      // Store completion data for modal
      setCompletionData({ duration, quality });

      // Show completion modal instead of alert
      setShowCompletionModal(true);

      // Trigger refresh
      if (onUpdate) onUpdate();

    } catch (error) {
      console.error("Error updating sleep log:", error);
      Alert.alert("Error", "Failed to update sleep log. Please try again.");
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{sleeplog.title}</Text>
          <Text style={styles.date}>{formatDate(sleeplog.createdAt)}</Text>
        </View>
        <View style={styles.statusContainer}>
          {!sleeplog.isCompleted ? (
            <TouchableOpacity style={styles.wakeUpButton} onPress={handleWakeUp}>
              <MaterialCommunityIcons name="weather-sunny" size={16} color="#fff" />
              <Text style={styles.wakeUpButtonText}>Wake Up</Text>
            </TouchableOpacity>
          ) : (
            <MaterialCommunityIcons
              name="check-circle"
              size={20}
              color="#4CAF50"
            />
          )}
        </View>
      </View>

      <View style={styles.timeContainer}>
        <View style={styles.timeItem}>
          <MaterialCommunityIcons
            name="sleep"
            size={16}
            color="#666"
            style={styles.timeIcon}
          />
          <Text style={styles.timeLabel}>Sleep:</Text>
          <Text style={styles.timeValue}>{formatTime(sleeplog.sleepTime)}</Text>
        </View>

        <View style={styles.timeItem}>
          <MaterialCommunityIcons
            name="weather-sunny"
            size={16}
            color="#666"
            style={styles.timeIcon}
          />
          <Text style={styles.timeLabel}>Wake:</Text>
          <Text style={styles.timeValue}>
            {sleeplog.isCompleted && sleeplog.wakeTime ? formatTime(sleeplog.wakeTime) : "Not set"}
          </Text>
        </View>
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Duration</Text>
          <Text style={styles.statValue}>
            {formatDuration(sleeplog.duration)}
          </Text>
        </View>

        {showQuality && sleeplog.quality && (
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>
              Quality
              {sleeplog.autoQuality && sleeplog.quality === sleeplog.autoQuality && (
                <Text style={styles.autoIndicator}> 🤖</Text>
              )}
            </Text>
            <SleepQualityDisplay
              quality={sleeplog.quality}
              showLabel={false}
              showStars={true}
              showEmoji={false}
              size="small"
            />
          </View>
        )}
      </View>

      {sleeplog.notes && (
        <View style={styles.notesContainer}>
          <Text style={styles.notesLabel}>Notes:</Text>
          <Text style={styles.notesText} numberOfLines={2}>
            {sleeplog.notes}
          </Text>
        </View>
      )}

      {/* Wake Time Picker */}
      {showWakeTimePicker && Platform.OS === "android" && (
        <DateTimePicker
          value={selectedWakeTime}
          mode="time"
          display="default"
          onChange={(_, selectedTime) => {
            if (selectedTime) {
              handleWakeTimeConfirm(selectedTime);
            } else {
              setShowWakeTimePicker(false);
            }
          }}
        />
      )}

      {Platform.OS === "ios" && (
        <Modal
          transparent
          animationType="slide"
          visible={showWakeTimePicker}
          onRequestClose={() => setShowWakeTimePicker(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerTitle}>Set Wake Up Time</Text>
              <DateTimePicker
                value={selectedWakeTime}
                mode="time"
                display="spinner"
                onChange={(_, selectedTime) => {
                  if (selectedTime) setSelectedWakeTime(selectedTime);
                }}
              />
              <View style={styles.pickerButtons}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowWakeTimePicker(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={() => handleWakeTimeConfirm(selectedWakeTime)}
                >
                  <Text style={styles.confirmButtonText}>Confirm</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      {/* Sleep Completion Modal */}
      <Modal
        visible={showCompletionModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowCompletionModal(false)}
      >
        <View style={styles.completionModalBackground}>
          <View style={styles.completionModalContainer}>
            <LottieView
              source={require("../../assets/files/sleeplogComplete.json")}
              autoPlay
              loop={false}
              style={{ width: 200, height: 200 }}
            />
            <Text style={styles.completionModalText}>Sleep Session Completed! 🌅</Text>
            <Text style={styles.completionModalSubtext}>
              {completionData ?
                `Duration: ${Math.floor(completionData.duration / 60)}h ${completionData.duration % 60}m` :
                "Sweet dreams!"
              }
            </Text>
            {completionData && (
              <Text style={styles.completionModalSubtext}>
                Quality: {completionData.quality}/5 stars
              </Text>
            )}
            <Pressable
              style={styles.completionModalCloseButton}
              onPress={() => setShowCompletionModal(false)}
            >
              <Text style={styles.completionModalBtnText}>Close</Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </TouchableOpacity>
  );
};

export default SleepLogCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    marginHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 2,
  },
  date: {
    fontSize: 12,
    color: "#888",
  },
  statusContainer: {
    marginLeft: 8,
  },
  timeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  timeItem: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  timeIcon: {
    marginRight: 6,
  },
  timeLabel: {
    fontSize: 12,
    color: "#666",
    marginRight: 4,
  },
  timeValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    marginBottom: 4,
  },
  autoIndicator: {
    fontSize: 10,
  },
  statValue: {
    fontSize: 14,
    fontWeight: "600",
    color: CrimsonLuxe.primary400,
  },
  wakeUpButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FF9800",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  wakeUpButtonText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-end",
  },
  pickerContainer: {
    backgroundColor: "#fff",
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 16,
    color: "#333",
  },
  pickerButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "500",
  },
  confirmButton: {
    flex: 1,
    backgroundColor: CrimsonLuxe.primary400,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  confirmButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  notesContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  notesLabel: {
    fontSize: 12,
    color: "#666",
    marginBottom: 4,
  },
  notesText: {
    fontSize: 13,
    color: "#555",
    lineHeight: 18,
  },
  completionModalBackground: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  completionModalContainer: {
    backgroundColor: "#fff",
    borderRadius: 20,
    padding: 24,
    alignItems: "center",
    marginHorizontal: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  completionModalText: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    textAlign: "center",
    marginBottom: 8,
  },
  completionModalSubtext: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
    marginBottom: 4,
  },
  completionModalCloseButton: {
    backgroundColor: CrimsonLuxe.primary400,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  completionModalBtnText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});

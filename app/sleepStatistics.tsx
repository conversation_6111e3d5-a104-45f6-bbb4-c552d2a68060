import React, { useCallback, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  FlatList,
} from "react-native";
import { useFocusEffect, router } from "expo-router";
import PageLayout from "@/components/pageLayout";
import BackButtonHeader from "@/components/backButtonHeader";
import SleepQualityDisplay from "@/components/sleepQualityDisplay";
import { CrimsonLuxe } from "@/constants/Colors";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import {
  getSleeplogStats,
  getRecentSleeplogs,
  getSleeplogCount,
} from "@/db/service/SleeplogService";

interface SleepStats {
  total: number;
  totalDuration: number;
  averageQuality: number;
  period?: string;
}

interface SleepLog {
  id: string;
  title: string;
  sleepTime: Date;
  wakeTime: Date;
  duration?: number;
  quality?: number;
  createdAt: Date;
}

const SleepStatisticsScreen = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<"weekly" | "monthly" | "allTime">("weekly");
  const [stats, setStats] = useState<SleepStats>({
    total: 0,
    totalDuration: 0,
    averageQuality: 0,
  });
  const [recentLogs, setRecentLogs] = useState<SleepLog[]>([]);
  const [overallStats, setOverallStats] = useState({
    total: 0,
    completed: 0,
    incomplete: 0,
  });

  const fetchStatistics = async () => {
    try {
      const [periodStats, recent, overall] = await Promise.all([
        getSleeplogStats(selectedPeriod),
        getRecentSleeplogs(5),
        getSleeplogCount(),
      ]);

      setStats(periodStats);
      setRecentLogs(recent);
      setOverallStats(overall);
    } catch (error) {
      console.error("Error fetching sleep statistics:", error);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchStatistics();
    }, [selectedPeriod])
  );

  const formatDuration = (minutes?: number) => {
    if (!minutes) return "0h 0m";
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString([], {
      month: "short",
      day: "numeric",
    });
  };

  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case "weekly":
        return "This Week";
      case "monthly":
        return "This Month";
      case "allTime":
        return "All Time";
      default:
        return "This Week";
    }
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {(["weekly", "monthly", "allTime"] as const).map((period) => (
        <TouchableOpacity
          key={period}
          style={[
            styles.periodButton,
            selectedPeriod === period && styles.selectedPeriod,
          ]}
          onPress={() => setSelectedPeriod(period)}
        >
          <Text
            style={[
              styles.periodText,
              selectedPeriod === period && styles.selectedPeriodText,
            ]}
          >
            {period === "allTime" ? "All Time" : period.charAt(0).toUpperCase() + period.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderStatsCard = () => (
    <View style={styles.statsCard}>
      <Text style={styles.statsTitle}>{getPeriodLabel()} Overview</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="sleep" size={24} color={CrimsonLuxe.primary400} />
          <Text style={styles.statValue}>{stats.total}</Text>
          <Text style={styles.statLabel}>Sleep Sessions</Text>
        </View>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="clock-outline" size={24} color="#4CAF50" />
          <Text style={styles.statValue}>{formatDuration(stats.totalDuration)}</Text>
          <Text style={styles.statLabel}>Total Sleep</Text>
        </View>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="star" size={24} color="#FFD700" />
          <Text style={styles.statValue}>{stats.averageQuality.toFixed(1)}</Text>
          <Text style={styles.statLabel}>Avg Quality</Text>
        </View>
        <View style={styles.statItem}>
          <MaterialCommunityIcons name="chart-line" size={24} color="#FF9800" />
          <Text style={styles.statValue}>
            {stats.total > 0 ? Math.round(stats.totalDuration / stats.total) : 0}m
          </Text>
          <Text style={styles.statLabel}>Avg Duration</Text>
        </View>
      </View>
    </View>
  );

  const renderOverallStats = () => (
    <View style={styles.overallCard}>
      <Text style={styles.overallTitle}>Overall Progress</Text>
      <View style={styles.overallGrid}>
        <View style={styles.overallItem}>
          <Text style={styles.overallValue}>{overallStats.total}</Text>
          <Text style={styles.overallLabel}>Total Logs</Text>
        </View>
        <View style={styles.overallItem}>
          <Text style={styles.overallValue}>{overallStats.completed}</Text>
          <Text style={styles.overallLabel}>Completed</Text>
        </View>
        <View style={styles.overallItem}>
          <Text style={styles.overallValue}>
            {overallStats.total > 0 
              ? Math.round((overallStats.completed / overallStats.total) * 100)
              : 0}%
          </Text>
          <Text style={styles.overallLabel}>Success Rate</Text>
        </View>
      </View>
    </View>
  );

  const renderRecentLog = ({ item }: { item: SleepLog }) => (
    <View style={styles.recentLogItem}>
      <View style={styles.recentLogHeader}>
        <Text style={styles.recentLogTitle}>{item.title}</Text>
        <Text style={styles.recentLogDate}>{formatDate(item.createdAt)}</Text>
      </View>
      <View style={styles.recentLogDetails}>
        <View style={styles.recentLogTime}>
          <Text style={styles.recentLogTimeText}>
            {formatTime(item.sleepTime)} - {formatTime(item.wakeTime)}
          </Text>
          <Text style={styles.recentLogDuration}>
            {formatDuration(item.duration)}
          </Text>
        </View>
        {item.quality && (
          <SleepQualityDisplay
            quality={item.quality}
            showLabel={false}
            showStars={true}
            size="small"
          />
        )}
      </View>
    </View>
  );

  // Create data array for FlatList
  const screenData = [
    { type: 'periodSelector', id: 'period' },
    { type: 'statsCard', id: 'stats' },
    { type: 'overallStats', id: 'overall' },
    { type: 'recentHeader', id: 'recentHeader' },
    ...(recentLogs.length > 0
      ? recentLogs.map((log, index) => ({ type: 'recentLog', id: `log-${index}`, data: log }))
      : [{ type: 'emptyRecent', id: 'empty' }]
    )
  ];

  const renderScreenItem = ({ item }: { item: any }) => {
    switch (item.type) {
      case 'periodSelector':
        return renderPeriodSelector();
      case 'statsCard':
        return renderStatsCard();
      case 'overallStats':
        return renderOverallStats();
      case 'recentHeader':
        return (
          <View style={styles.recentSection}>
            <View style={styles.recentHeader}>
              <Text style={styles.recentTitle}>Recent Sleep Logs</Text>
              <TouchableOpacity onPress={() => router.push("/sleepLogs")}>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>
          </View>
        );
      case 'recentLog':
        return renderRecentLog({ item: item.data });
      case 'emptyRecent':
        return (
          <View style={[styles.recentSection, { marginTop: 0 }]}>
            <View style={styles.emptyRecent}>
              <Text style={styles.emptyRecentText}>No recent sleep logs</Text>
            </View>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <PageLayout style={styles.container} scrollable={false}>
      <BackButtonHeader title="Sleep Statistics" />

      <FlatList
        data={screenData}
        keyExtractor={(item) => item.id}
        renderItem={renderScreenItem}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </PageLayout>
  );
};

export default SleepStatisticsScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    flex: 1,
  },
  periodSelector: {
    flexDirection: "row",
    margin: 16,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: "center",
  },
  selectedPeriod: {
    backgroundColor: CrimsonLuxe.primary400,
  },
  periodText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  selectedPeriodText: {
    color: "#fff",
    fontWeight: "600",
  },
  statsCard: {
    backgroundColor: "#fff",
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
    textAlign: "center",
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  statItem: {
    width: "48%",
    alignItems: "center",
    marginBottom: 16,
    padding: 12,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  overallCard: {
    backgroundColor: "#fff",
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  overallTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
    textAlign: "center",
  },
  overallGrid: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  overallItem: {
    alignItems: "center",
  },
  overallValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: CrimsonLuxe.primary400,
    marginBottom: 4,
  },
  overallLabel: {
    fontSize: 12,
    color: "#666",
  },
  recentSection: {
    margin: 16,
    marginTop: 0,
  },
  recentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  recentTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  viewAllText: {
    fontSize: 14,
    color: CrimsonLuxe.primary400,
    fontWeight: "500",
  },
  recentLogItem: {
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  recentLogHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  recentLogTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
  },
  recentLogDate: {
    fontSize: 12,
    color: "#666",
  },
  recentLogDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  recentLogTime: {
    flex: 1,
  },
  recentLogTimeText: {
    fontSize: 12,
    color: "#666",
    marginBottom: 2,
  },
  recentLogDuration: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
  },
  emptyRecent: {
    padding: 20,
    alignItems: "center",
  },
  emptyRecentText: {
    fontSize: 14,
    color: "#666",
  },
});

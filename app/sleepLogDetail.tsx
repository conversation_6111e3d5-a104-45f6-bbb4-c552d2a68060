import React, { useCallback, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  ScrollView,
} from "react-native";
import { useFocusEffect, router, useLocalSearchParams } from "expo-router";
import PageLayout from "@/components/pageLayout";
import BackButtonHeader from "@/components/backButtonHeader";
import SleepQualityDisplay from "@/components/sleepQualityDisplay";
import { CrimsonLuxe } from "@/constants/Colors";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import {
  getSleeplogById,
  deleteSleeplog,
  updateSleeplog,
} from "@/db/service/SleeplogService";
import Realm from "realm";

interface SleepLog {
  id: string;
  title: string;
  sleepTime: Date;
  wakeTime?: Date;
  isCompleted: boolean;
  notes?: string;
  duration?: number;
  quality?: number;
  createdAt: Date;
}

const SleepLogDetailScreen = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [sleepLog, setSleepLog] = useState<SleepLog | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchSleepLog = async () => {
    if (!id) return;
    
    try {
      const log = await getSleeplogById(id);
      if (log) {
        setSleepLog({
          id: log._id,
          title: log.title,
          sleepTime: new Date(log.sleepTime),
          wakeTime: new Date(log.wakeTime),
          isCompleted: log.isCompleted,
          notes: log.notes,
          duration: log.duration,
          quality: log.quality,
          createdAt: new Date(log.createdAt),
        });
      }
    } catch (error) {
      console.error("Error fetching sleep log:", error);
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchSleepLog();
    }, [id])
  );

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return "N/A";
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const handleToggleCompleted = async () => {
    if (!sleepLog) return;

    try {
      await updateSleeplog(new Realm.BSON.ObjectId(sleepLog.id), {
        isCompleted: !sleepLog.isCompleted,
      });
      setSleepLog({ ...sleepLog, isCompleted: !sleepLog.isCompleted });
    } catch (error) {
      console.error("Error updating sleep log:", error);
      Alert.alert("Error", "Failed to update sleep log");
    }
  };

  const handleDelete = () => {
    Alert.alert(
      "Delete Sleep Log",
      "Are you sure you want to delete this sleep log? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              await deleteSleeplog(new Realm.BSON.ObjectId(sleepLog!.id));
              router.back();
            } catch (error) {
              console.error("Error deleting sleep log:", error);
              Alert.alert("Error", "Failed to delete sleep log");
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <PageLayout style={styles.container}>
        <BackButtonHeader title="Sleep Log" />
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </PageLayout>
    );
  }

  if (!sleepLog) {
    return (
      <PageLayout style={styles.container}>
        <BackButtonHeader title="Sleep Log" />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Sleep log not found</Text>
        </View>
      </PageLayout>
    );
  }

  return (
    <PageLayout style={styles.container}>
      <BackButtonHeader title="Sleep Log" />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{sleepLog.title}</Text>
            <TouchableOpacity
              style={styles.statusButton}
              onPress={handleToggleCompleted}
            >
              <MaterialCommunityIcons
                name={sleepLog.isCompleted ? "check-circle" : "clock-outline"}
                size={20}
                color={sleepLog.isCompleted ? "#4CAF50" : "#FF9800"}
              />
              <Text
                style={[
                  styles.statusText,
                  { color: sleepLog.isCompleted ? "#4CAF50" : "#FF9800" },
                ]}
              >
                {sleepLog.isCompleted ? "Completed" : "Incomplete"}
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.date}>{formatDate(sleepLog.createdAt)}</Text>
        </View>

        <View style={styles.timeCard}>
          <Text style={styles.cardTitle}>Sleep Schedule</Text>
          <View style={styles.timeRow}>
            <View style={styles.timeItem}>
              <MaterialCommunityIcons name="sleep" size={24} color="#666" />
              <Text style={styles.timeLabel}>Sleep Time</Text>
              <Text style={styles.timeValue}>{formatTime(sleepLog.sleepTime)}</Text>
            </View>
            <View style={styles.timeItem}>
              <MaterialCommunityIcons name="weather-sunny" size={24} color="#666" />
              <Text style={styles.timeLabel}>Wake Time</Text>
              <Text style={styles.timeValue}>
                {sleepLog.wakeTime ? formatTime(sleepLog.wakeTime) : "Not set"}
              </Text>
            </View>
          </View>
          <View style={styles.durationContainer}>
            <Text style={styles.durationLabel}>Total Duration</Text>
            <Text style={styles.durationValue}>
              {formatDuration(sleepLog.duration)}
            </Text>
          </View>
        </View>

        {sleepLog.quality && (
          <View style={styles.qualityCard}>
            <Text style={styles.cardTitle}>Sleep Quality</Text>
            <SleepQualityDisplay
              quality={sleepLog.quality}
              showLabel={true}
              showStars={true}
              showEmoji={true}
              size="large"
              style={styles.qualityDisplay}
            />
          </View>
        )}

        {sleepLog.notes && (
          <View style={styles.notesCard}>
            <Text style={styles.cardTitle}>Notes</Text>
            <Text style={styles.notesText}>{sleepLog.notes}</Text>
          </View>
        )}

        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
            <MaterialCommunityIcons name="delete" size={20} color="#fff" />
            <Text style={styles.deleteButtonText}>Delete</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </PageLayout>
  );
};

export default SleepLogDetailScreen;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF",
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 16,
    color: "#666",
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  titleContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: "600",
    color: "#333",
    flex: 1,
  },
  statusButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
  date: {
    fontSize: 14,
    color: "#666",
  },
  timeCard: {
    backgroundColor: "#fff",
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
  },
  timeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  timeItem: {
    alignItems: "center",
    flex: 1,
  },
  timeLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 8,
    marginBottom: 4,
  },
  timeValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  durationContainer: {
    alignItems: "center",
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  durationLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  durationValue: {
    fontSize: 20,
    fontWeight: "bold",
    color: CrimsonLuxe.primary400,
  },
  qualityCard: {
    backgroundColor: "#fff",
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  qualityDisplay: {
    marginTop: 8,
  },
  notesCard: {
    backgroundColor: "#fff",
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  notesText: {
    fontSize: 14,
    color: "#555",
    lineHeight: 20,
  },
  actionButtons: {
    margin: 16,
    marginTop: 0,
  },
  deleteButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F44336",
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  deleteButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
});
